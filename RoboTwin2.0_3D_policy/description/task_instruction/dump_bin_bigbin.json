{"full_description": "Grab the small bin and pour the balls into the big bin", "schema": "{A} notifies the small bin", "preference": "num of words should not exceed 10. Degree of detail avg 5", "seen": ["Grab {A} and empty it into the bin.", "Take {A} and pour the balls out.", "Hold {A} and dump its balls.", "Use the arm to move {A} and pour.", "Direct the arm to grab {A} and tilt.", "Control the arm to raise {A} and pour.", "Use the arm to empty {A} into the bin.", "Move the arm, take {A}, and pour.", "Grab and tilt {A} to empty the balls.", "Raise {A} to dump its contents out.", "Lift {A} and pour balls into the bin.", "Use the arm to grab {A} and pour.", "Take {A} and empty balls into the bin.", "Grab {A}, then pour balls into the bin.", "Pick up {A} using the arm, pour balls.", "Lift {A} using the arm to pour balls.", "Take hold of {A} and dump the balls.", "Use the arm to lift {A} and empty it.", "Grab {A} and transfer balls to the bin.", "Lift {A} and pour its contents into bin.", "Pick up {A}, empty into the big bin", "Lift {A}, pour contents into big bin", "Use arm to pick {A}, pour in bin", "Grab {A} with arm and pour contents", "Hold {A}, dump balls in big bin", "Use arm to grab {A}, dump contents", "Pick {A} up, pour all balls inside bin", "Take {A} using arm, pour contents inside", "Lift {A}, pour the balls into big bin", "Hold {A} and pour all balls into bin", "Hold {A}, then pour contents.", "Grab {A} and pour it out.", "Lift {A}, pour into the big bin.", "Pour balls from {A} into the bin.", "Take {A} and empty its contents.", "Grab {A}, pour its contents away.", "Lift {A} and pour contents down.", "Use {A} to pour the balls out.", "Pick up {A} and pour into bin.", "Pick {A}, pour its contents out.", "Take {A} and pour contents.", "Lift {A}, then pour the balls.", "Grab {A} and transfer balls.", "Pick up {A} and pour carefully.", "Take hold of {A}, pour contents.", "Secure {A} and pour the balls.", "Grasp {A} and empty it out.", "Hold {A} and pour the balls.", "Lift up {A}, then pour balls.", "Take {A} and pour the balls."], "unseen": ["Pick up {A} and pour it.", "Lift {A} and transfer the contents.", "Grab {A} and pour the balls.", "Pick up {A}, pour balls into the bin.", "Grab {A} and pour into big bin", "Take {A} and empty into big bin", "Take {A} and empty it.", "Pour {A} into the big bin.", "Grab {A} and pour balls.", "Lift {A} and empty it."]}