{"full_description": "use appropriate arm to move the stapler to a colored mat", "schema": "{A} notifies the stapler, {B} notifies the color of the mat(YOU SHOULD SAY {B} mat, or {B} colored mat), {a} notifies the arm to grab the stapler", "preference": "num of words should not exceed 10", "seen": ["Grab {A} and drop it on {B} mat.", "{a} moves {A} to the {B} mat.", "Set {A} onto the {B} colored mat.", "{a} places {A} on the {B} mat.", "Drop {A} onto the {B} mat.", "Stick {A} onto the {B} colored mat.", "{a} grabs {A} and sets it on {B} mat.", "Slide {A} to the {B} colored mat.", "{a} transfers {A} to the {B} mat.", "Stick {A} on the {B} mat.", "Grab {A} using {a} and set it on {B} mat.", "Move {A} to the {B} mat.", "Transfer {A} to the {B} colored mat.", "Use {a} and place {A} on {B} mat.", "Set {A} on the {B} mat with {a}.", "Position {A} on the {B} mat.", "Pick {A} using {a} and move it to {B} mat.", "Place {A} onto the {B} colored mat.", "Relocate {A} to the {B} mat.", "Grab {A} with {a} and drop it on {B} mat.", "Grab {A}, place it on the {B} mat", "Using {a}, set {A} on the {B} colored mat", "Put {A} on the {B} mat", "Lift {A} to the {B} mat using {a}", "Place {A} onto the {B} colored mat", "Set {A} down on the {B} mat", "With {a}, position {A} on the {B} mat", "Transfer {A} to the {B} mat", "Move {A} with {a} to the {B} mat", "Drop {A} carefully on the {B} mat", "Place {A} on the {B} mat using {a}", "Lift {A} and drop it onto {B} mat", "Shift {A} manually to the {B} mat", "Move {A} to the {B} mat with {a}", "Grab {A} and stick it onto {B} mat", "Use {a} to grab {A} and place it on {B} mat", "Pick {A} up and position it on {B} mat", "Carry {A} and drop it onto the {B} mat", "Use {a} to shift {A} onto the {B} mat", "Pick {A} with {a} and place it on {B} mat", "Use {a} to grab {A} and move it", "Set {A} down on the {B} mat", "Pick up {A} and place it on {B} mat", "Grab {A} using {a} and shift it to {B}", "Relocate {A} to the {B} colored mat", "Use {a} to place {A} onto the {B} mat", "Shift {A} to the {B} mat", "Pick up {A} with {a} and set it on {B}", "Carry {A} to the {B} colored mat", "With {a}, move {A} to the {B} mat"], "unseen": ["Move {A} to the {B} mat.", "Place {A} on the {B} colored mat.", "Use {a} to move {A} to {B} mat.", "Place {A} on the {B} colored mat.", "Use {a} to move {A} to the {B} mat", "Move {A} to the {B} colored mat", "Grab {A} and set it on {B} mat", "Use {a} to move {A} to {B} mat", "Move {A} to the {B} mat", "Place {A} on the {B} mat"]}