{"full_description": "if there is one bread on the table, use one arm to grab the bread and put it in the basket, if there are two breads on the table, use two arms to simultaneously!!! grab up two breads and put them in the basket", "schema": "{A} notifies the basket, {B} notifies the first bread(or the only bread if there is only one bread), {C} notifies the second bread(if there are two breads), {a} notifies the arm to grab the bread(may be left, right, or dual)", "preference": "num of words should not exceed 10. Degree of detail avg is six. NOTE!! 50% of the instructions are about one bread scenario, 50% of the instructions are about two breads scenario", "seen": ["Pick up {B} and put it in {A}.", "Use {a} to grab {B} and drop it inside {A}.", "Grab {B} with one hand and set it in {A}.", "Pick up both {B} and {C}, then place them in {A}.", "Simultaneously grab {B} and {C} using {a}, then drop them in {A}.", "Take {B} and {C} together and place them into {A}.", "Lift {B} and {C} at once with {a}, then set them in {A}.", "Pick both breads and place them into {A}.", "Use {a} to grab both breads, then put them in {A}.", "Grab {B} and {<PERSON>} quickly and drop them into {A}.", "Pick up {B} and drop it in {A}.", "Use both {a} to grab {B} and {C}.", "Pick {B} and {<PERSON>} and set them in {A}.", "Use {a} to place {B} and {C} into {A}.", "Pick {B} and put it into {A}.", "Grab {B} with {a} and drop it in {A}.", "Grab two breads {B} and {C} and place in {A}.", "Simultaneously use {a} to drop {B} and {C} in {A}.", "Pick {B} and move it to {A}.", "Grab both {B} and {C} with {a} and place in {A}.", "Lift {B} and transfer to {A}.", "Move {B} to {A} using one arm.", "Grab {B}, drop it into {A}.", "Use two arms to grab {B} and {C}.", "Pick {B} and {<PERSON>}, place them in {A}.", "Simultaneously grab {B} and {C}, drop in {A}.", "Move {B} and {C} at once into {A}.", "With both arms, grab {B} and {C}.", "Shift {B} and {C} together to {A}.", "Put {B} and {C} into {A} using two arms.", "Lift {B} and set it in {A}.", "Put {B} into {A} using an arm.", "Take {B} and {C} then place in {A}.", "Use two arms and set {B}, {C} in {A}.", "Grab both {B} and {C}, drop into {A}.", "Lift {B} and {C} with two arms, put in {A}.", "Put {B} into {A} after grabbing it.", "Grab {B} with an arm and set in {A}.", "Take {B} and {C}, place them inside {A}.", "Use both arms to move {B}, {C} to {A}.", "Use {a} to grab {B} for {A}", "Drop {B} into {A}", "Simultaneously grab {B} and {C}", "Move {B} and {C} to {A}", "Use {a} to pick and place {B} {C}", "Shift {B} and {C} into {A}", "Pick {B} and {C} for the {A}", "Grab {B} for {A} with {a}", "Take {B} and {C} to {A}", "Place {B} and {C} in {A} using {a}"], "unseen": ["Grab {B} and drop it into {A}.", "Use {a} to pick up {B}, then place it in {A}.", "Grab {B} and put it in {A}.", "Use {a} to pick {B} and place in {A}.", "Pick {B} and place it in {A}.", "Use one arm to grab {B}, drop in {A}.", "Grab {B} and drop it into {A}.", "Grab {B} with one arm, place in {A}.", "Pick {B} and drop it in {A}", "Place {B} into {A} using {a}"]}