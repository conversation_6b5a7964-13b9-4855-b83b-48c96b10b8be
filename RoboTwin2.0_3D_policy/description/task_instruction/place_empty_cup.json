{"full_description": "use an arm to place the empty cup on the coaster", "schema": "{A} notifies the empty cup, {B} notifies the coaster", "preference": "num of words should not exceed 10", "seen": ["Move {A} to {B}", "Place {A} on top of {B}", "Slide {A} onto {B}", "Set {A} down on {B}", "Use an arm to drop {A} on {B}", "Stick {A} onto {B} using the arm", "Position {A} carefully on {B}", "Use arm to place {A} on {B}", "Set {A} carefully onto {B}", "Drop {A} firmly on {B} using arm", "Use the arm to set {A} on {B}", "Lift {A} and place it over {B}", "Move {A} onto {B} with the arm", "Align {A} and set it down on {B}", "Use an arm to place {A} on {B}", "Grab {A} and lower it onto {B}", "Position {A} above and drop it on {B}", "Using the arm, align {A} over {B}", "Relocate {A} precisely onto {B} with arm", "Carefully place {A} onto {B} without arm", "Use the arm to put {A} onto {B}.", "Pick {A} up and drop it on {B}.", "Grab {A}, then place it onto {B}.", "Move {A} to {B} using the arm.", "Stick {A} onto {B} using the arm.", "Drop {A} on top of {B}.", "Slide {A} onto {B} with the arm.", "Use the arm to slide {A} onto {B}.", "Grab {A} and set it onto {B}.", "Set {A} on {B} using the arm.", "Move {A} to {B} and set it down.", "Using the arm, drop {A} onto {B}.", "Grab {A} and place it on {B}.", "Set {A} securely on {B} using the arm.", "Use the arm to position {A} on {B}.", "Stick {A} onto {B} carefully.", "Position {A} on {B} with the arm.", "Direct {A} to {B} and let it rest.", "Lift {A} and place it onto {B}.", "Using the arm, slide {A} onto {B}.", "Use an arm to drop {A} on {B}.", "Lift {A} and stick it onto {B}.", "Grab {A} and place it over {B}.", "Move {A} using the arm onto {B}.", "Slide {A} gently onto {B} via the arm.", "Make sure {A} ends up on {B}.", "Position {A} squarely on top of {B}.", "Use the arm to position {A} onto {B}.", "Lift {A} up and drop it on {B}.", "Set {A} onto {B} using the arm."], "unseen": ["Place {A} onto {B}", "Use arm to set {A} on {B}", "Place {A} carefully onto {B}", "Pick {A} and position it on {B}", "Place {A} onto {B}.", "Set {A} on top of {B}.", "Place {A} onto {B} gently.", "Set {A} on top of {B}.", "Set {A} down on {B}.", "Place {A} on top of {B}."]}