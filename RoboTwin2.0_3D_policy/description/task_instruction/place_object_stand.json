{"full_description": "use appropriate arm to place the object on the stand", "schema": "{A} notifies the object, {B} notifies the stand, {a} notifies the arm to grab the object", "preference": "num of words should not exceed 10", "seen": ["Grab {A} and set it on {B}", "Pick {A} and position it on {B}", "Move {A} using {a} and place on {B}", "Set {A} on {B} using {a}", "Grab and put {A} on {B}", "Lift {A} and position on {B}", "Position {A} on {B} with {a}", "Pick {A} up and place on {B}", "Grab {A} with {a} and move to {B}", "Take {A} and set it on {B}", "Use {a} to position {A} on {B}.", "Move {A} onto {B}.", "Grab {A} with {a} and place on {B}.", "Set {A} in position on {B}.", "Use {a} to move {A} onto {B}.", "Place {A} on {B}.", "Transfer {A} using {a} to {B}.", "Move {A} to {B} using {a}.", "Position {A} on {B}.", "Place {A} precisely on {B}.", "Grab {A} and set it onto {B}.", "Set {A} in position on {B}.", "Pick {A} with {a} and place on {B}.", "Transfer {A} to {B} securely with {a}.", "Move {A} to {B} and set it there.", "Carefully place {A} onto {B}.", "Lift {A} with {a} and position on {B}.", "Grab and place {A} directly on {B}.", "Pick up {A} and drop it on {B}.", "Use {a} to lift {A} and set on {B}.", "Pick up {A} with {a} and set it on {B}", "Lift {A} and position it on {B}", "Select {a}, grab {A}, and move it to {B}", "Put {A} on {B} after picking it", "Grab {A} using {a} and place it on {B}", "Move {A} to {B} and release it", "Use {a} to lift {A} and set it on {B}", "Place {A} on {B} after grabbing it", "With {a}, pick {A} and position it on {B}", "Set {A} on {B} after moving it", "Pick up {A} and set it on {B}.", "Place {A} precisely on top of {B}.", "Use {a} to grab {A} and place on {B}.", "Lift {A} with {a} and align it on {B}.", "Grab and move {A} to position it on {B}.", "Locate {A}, pick it up, and place on {B}.", "Pick up {A} using {a} and set it on {B}.", "Take {A} with {a} and put it on {B}.", "Pick {A} and place it carefully onto {B}.", "Bring {A} to {B} and set it in place."], "unseen": ["Use {a} to place {A} on {B}", "Place {A} onto {B} with {a}", "Place {A} on {B} with {a}.", "Set {A} on {B}.", "Use {a} to place {A} on {B}.", "Place {A} on {B} using {a}.", "Use {a} to grab {A} and place it on {B}", "Grab {A}, then place it on {B}", "Grab {A} using {a} and place on {B}.", "Set {A} onto {B} using the right arm."]}