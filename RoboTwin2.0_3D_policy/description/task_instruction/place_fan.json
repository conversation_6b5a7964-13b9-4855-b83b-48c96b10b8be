{"full_description": "grab the fan and place it on a colored mat, <make sure the fan is facing the robot!(THIS MUST BE REFERRED TO>", "schema": "{A} notifies the fan,{B} notifies the color of the mat(YOU SHOULD SAY {B} mat, or {B} colored mat), {a} notifies the arm to grab the fan", "preference": "num of words should not exceed 15", "seen": ["Place {A} on the {B} mat after grabbing it with {a} and align it toward the robot.", "Grab {A} with {a} and ensure it's positioned on the {B} mat facing the robot.", "Grab {A} and position it on the {B} mat, ensuring it faces the robot.", "Lift {A}, place it on the {B} mat, and ensure it's facing the robot.", "Use {a} to pick {A}, set it on the {B} mat, and face it toward the robot.", "Grab {A} and carefully place it on the {B} mat facing toward the robot.", "Pick up {A} with {a}, place it on the {B} mat, and turn it toward the robot.", "Lift {A} and set it on the {B} mat, ensuring it faces the robot.", "Use {a} to grab {A}, then align it on the {B} mat facing the robot.", "Pick {A}, place it on the {B} mat, and ensure it points toward the robot.", "Use {a} to grab {A}, put it on the {B} mat, and face it toward the robot", "Lift {A} with {a}, place it on the {B} mat, and point it at the robot", "Set {A} on the {B} mat and make sure it faces the robot", "With {a}, grab {A} and position it on the {B} mat facing the robot", "Take {A}, place it on the {B} mat, ensure it points at the robot", "Grab {A} with {a}, set it on the {B} mat, and align it to face the robot", "Lift {A} and put it on the {B} mat so it faces the robot", "Use {a} to pick {A}, set it on the {B} mat, and direct it toward the robot", "Place {A} on the {B} mat and confirm it is pointing at the robot", "Take {A} with {a}, put it on the {B} mat, and make it face the robot", "Use {a} to pick up {A} and place it on {B} mat.", "Pick up {A} and ensure it faces the robot on the {B} mat.", "Set {A} onto the {B} colored mat, oriented towards the robot.", "Grab {A} with {a}, making sure it faces the robot on the {B} mat.", "Place {A} on the {B} mat and position it to face the robot.", "Lift {A} using {a} and put it on the {B} mat facing the robot.", "Position {A} on the {B} mat so it faces the robot.", "Grab {A} with {a}, place it on the {B} mat, ensure it faces the robot.", "Pick up {A} and place it on the {B} mat with it facing the robot.", "Use {a} to grab {A}, set it on {B} mat, and make it face the robot.", "Pick {A}, align it toward the robot, and drop it on the {B} mat.", "With {a}, grab {A}, align it to face the robot, and put it on the {B} mat.", "Pick up {A} and place it on the {B} mat ensuring it faces the robot.", "Grab {A} using {a} and set it on the {B} colored mat, facing the robot.", "Grab {A}, position it to face the robot, and place it on the {B} mat.", "Pick {A} with {a}, ensure it faces the robot, and put it on the {B} mat.", "Lift {A}, align it toward the robot, and position it on the {B} mat.", "Using {a}, grab {A}, face it towards the robot, and set it on the {B} mat.", "Take {A} and place it on the {B} mat, making sure it faces the robot.", "Pick {A} with {a}, align it to face the robot, and set it on the {B} mat.", "Place {A} on the {B} mat and ensure it faces the robot.", "Using {a}, grab {A} and put it on the {B} mat facing the robot.", "Set {A} on the {B} colored mat ensuring it faces the robot.", "Grab {A} using {a} and place it on the {B} mat ensuring it faces the robot.", "Place {A} on the {B} mat and verify it is facing the robot.", "Pick {A} with {a} and set it on the {B} mat facing the robot.", "Put {A} on the {B} mat and make sure it faces the robot.", "Grab {A} using {a} and position it on the {B} mat facing the robot.", "Place {A} on the {B} colored mat ensuring it faces the robot.", "Using {a}, grab {A} and set it on the {B} mat facing the robot."], "unseen": ["Pick up {A} and set it on the {B} mat facing the robot.", "Use {a} to grab {A}, then place it on the {B} mat facing the robot.", "Grab {A} and set it on the {B} mat facing the robot", "Pick {A}, place it on the {B} mat, face it toward the robot", "Grab {A} and set it on the {B} mat.", "Place {A} onto the {B} colored mat facing the robot.", "Grab {A} and set it on the {B} mat facing the robot.", "Use {a} to grab {A} and place it on the {B} mat facing the robot.", "Pick {A} and set it on the {B} mat facing the robot.", "Grab {A} with {a} and position it on the {B} mat facing the robot."]}