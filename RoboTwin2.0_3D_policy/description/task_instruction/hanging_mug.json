{"full_description": "Use left arm to pick the mug on the table, rotate the mug and put the mug down in the middle of the table, use the right arm to pick the mug and hang it onto the rack.", "schema": "{A} notifies the mug, {B} notifies the rack", "preference": "num of words should not exceed 15", "seen": ["Use the left arm to grab {A}, rotate it, set it down, then hang {A} onto {B}.", "Lift {A}, turn it, place it back, and move it onto {B} with the right arm.", "Take {A}, rotate it, place it in the center, then hang it on {B}.", "Grab {A}, rotate it, place it in the middle, and hang it on {B}.", "Use your left arm to pick {A}, rotate it, set it down, and hang it onto {B}.", "Use the right arm to lift {A}, rotate it, and attach it to {B} after setting it down.", "Pick {A}, turn it, put it in the center, and transfer it to {B}.", "Lift {A} from the table, rotate it, place it in the middle, and hang it on {B}.", "Use the left arm to grab {A}, flip it, set it down, and then attach it to {B}.", "Take {A}, rotate it, place it back on the table, and hang it on {B} afterward.", "Using the left arm, pick {A}, turn it, place it down, and hang it on {B}.", "Pick {A} up, twist it, place it back, then hang it onto {B}.", "With the left arm, grab {A}, spin it, place it down, then use the right arm to hang it on {B}.", "Take {A}, rotate it, set it in the middle, then hang it on {B}.", "Using your left arm, lift {A}, rotate it, place it down, then your right arm to hang it onto {B}.", "Grab {A}, turn it around, put it back, then attach it to {B}.", "Lift {A}, twist it, set it back, and secure it onto {B}.", "Use the left arm to grab {A}, rotate it, place it down, and hang it onto {B} with the right arm.", "Take {A} from the table, spin it, place it in the center, then hang it on {B}.", "Pick {A} using your left arm, turn it, set it on the table, and hang it on {B} with the right arm.", "Grab {A}, rotate it, place it on the table, and hook it onto {B}.", "Use the left arm to pick {A}, turn it, place it, then hang on {B}.", "Pick {A} from the table using one hand, rotate, and hang it on {B}.", "Grab {A} from the table, rotate it, place it, and hang it onto {B}.", "Use your left arm to grab {A}, rotate it, set it down, then hang on {B}.", "Take {A} from the table, turn it, set it down, and attach it to {B}.", "With the left arm, pick {A}, rotate it, place it, then hang it onto {B}.", "Lift {A}, turn it around, set it in the middle, and place it on {B}.", "Use the left arm to lift {A}, rotate it, put it down, then hook onto {B}.", "Pick {A} from the table, rotate it, place it, and then hang it onto {B}.", "Pick {A}, turn it, and leave it in the table’s center.", "Lift {A} with one arm, rotate, and drop it on the table.", "Pick {A}, rotate it, and place it in the table middle.", "Use one arm to grab {A}, rotate, and place it down.", "Lift {A}, rotate, and center it on the table.", "Take {A} with one arm, turn it, and set it in the center.", "Grab {A}, twist it, then place it on the table’s center.", "Use one arm to move {A}, rotate it, and position it on the rack.", "Lift {A}, give it a turn, and hang it onto {B}.", "Take {A} with one arm, rotate it, and hang it onto {B}.", "Use the left arm to grab {A}, rotate it, place it in the middle, then use the right arm to hang it onto {B}.", "Lift {A} from the table, turn it, put it down in the middle, then hang it onto {B}.", "Take {A}, rotate it, set it on the table's center, then hang it onto {B}.", "Use your left arm to grab {A}, rotate it, place it in the middle, then use the right arm to hang it onto {B}.", "Pick up {A}, turn it, place it centrally, then hang {A} onto {B}.", "With the left arm, grab {A} from the table, rotate it, place it centrally, and with the right arm, hang it onto {B}.", "Grab {A}, rotate it, place it in the middle, and hang it onto {B}.", "Use one arm to grab {A}, turn it, set it centrally, and use the other to hang it onto {B}.", "Lift {A}, rotate it, put it down in the table's center, then hang it onto {B}.", "Take {A} from the table, rotate it, place it in the center, then hang it onto {B}."], "unseen": ["Grab {A} from the table, rotate it, and set it in the center. Then hang {A} on {B}.", "Pick up {A}, rotate it, place it on the table, and hang it on {B}.", "Grab {A}, turn it, set it on the table, then hang it on {B}.", "Lift {A}, rotate it, put it down, then attach it to {B}.", "Pick {A} with the left arm, rotate, place it, then hang it on {B}.", "Lift {A} from the table, spin it, set it down, and hang it on {B}.", "Grab {A} from the table, rotate, and set it down.", "Use one arm for {A}, rotate, and place it on the table.", "Grab {A} on the table, rotate it, set it down in the middle, then hang it onto {B}.", "Pick {A} from the table, rotate it, place it in the middle, and hang it onto {B}."]}