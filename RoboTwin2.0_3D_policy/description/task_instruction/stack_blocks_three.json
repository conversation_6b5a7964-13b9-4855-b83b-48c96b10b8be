{"full_description": "there are three blocks on the table, the color of the blocks is <red, green and blue>, <move the blocks to the center of the table>, and <stack the blue block on the green block, and the green block on the red block>", "schema": "{A} notifies the red block, {B} notifies the green block, {C} notifies the blue block, {a} notifies the arm to manipulate the red block, {b} notifies the arm to manipulate the green block, {c} notifies the arm to manipulate the blue block", "preference": "num of words should not exceed 20. Degree of detail avg 8", "seen": ["Shift {A}, {B}, {C} to the table's center, then stack {C} on {B}, and {B} on {A}.", "Stack {C} over {B} and {B} over {A} after moving all blocks to the center.", "Use {a}, {b}, {c} to place {A}, {B}, {C} at the center and stack them accordingly.", "Grab {A}, {B}, and {C} using {a}, {b}, {c}, move them to the center, then stack them.", "Move {A}, {B}, and {C} to the center using {a}, {b}, {c}, and stack them with {C} on top.", "Use {a}, {b}, and {c} to center {A}, {B}, and {C}, then stack {C} above {B} and {B} above {A}.", "Relocate {A}, {B}, and {C} to the center and stack {C} on {B} and {B} on {A}.", "Reposition {A}, {B}, and {C} to the middle and arrange {C} above {B} and {B} above {A}.", "Center {A}, {B}, and {C}, then stack them with {C} on {B} and {B} on {A}.", "Place {A}, {B}, and {C} at the center and stack {C} on {B}, then {B} on {A}.", "Place {A}, {B}, and {C} at the table's center; stack {C} over {B}, then {B} over {A}.", "Use {a}, {b}, and {c} to move {A}, {B}, {C} to the center and stack {C} on {B}, {B} on {A}.", "With {a}, {b}, and {c}, shift {A}, {B}, and {C} to the center and arrange {C} over {B}, {B} on {A}.", "Use arms {a}, {b}, and {c} to centralize {A}, {B}, {C} and stack {C} above {B}, then {B} above {A}.", "Centralize {A}, {B}, and {C} before stacking {C} on {B} and {B} on {A}.", "Move {A}, {B}, and {<PERSON>} to the middle first, then stack {C} on {B} and {B} on {A}.", "Arrange {A}, {B}, and {C} in the table's center and stack {C} atop {B}, then {B} atop {A}.", "With {a}, {b}, {c}, position {A}, {B}, {C} at the table's center and stack {C} on {B}, {B} on {A}.", "Using {a}, {b}, {c}, place {A}, {B}, {C} centrally and stack {C} atop {B}, then {B} atop {A}.", "Position {A}, {B}, and {C} in the center and stack {C} on {B}, followed by {B} on {A}.", "Bring {A}, {B}, and {C} to the center and stack {B} over {A}, {C} over {B}.", "Use {a}, {b}, and {c} to move {A}, {B}, and {C} to the center, then stack {C} on {B} and {B} on {A}.", "Relocate {A}, {B}, and {C} to the center with {a}, {b}, {c}, and stack {C} on {B}, {B} on {A}.", "Shift {A}, {B}, and {C} to the center using {a}, {b}, {c}, then pile {C} on {B}, {B} on {A}.", "Move {A}, {B}, and {C} to the center and stack {B} on {A}, {C} on {B}.", "Bring {A}, {B}, and {C} to the table's center and arrange them by stacking {C} over {B} and {B} over {A}.", "Place {A}, {B}, {C} in the middle and stack them using {a}, {b}, {c}, {B} on {A}, {C} on {B}.", "Adjust {A}, {B}, {C} to the center and use {a}, {b}, {c} to stack {C} on {B}, {B} on {A}.", "Reposition {A}, {B}, and {C} to the center, stacking {B} on {A} and {C} on {B}.", "With {a}, {b}, {c}, move {A}, {B}, {C} to the center and stack {B} on {A}, {C} on {B}.", "Place {A}, {B}, and {C} at the center, then stack {C} onto {B} and {B} onto {A}.", "Gather {A}, {B}, and {C} at the table's center and stack {C} on {B}, then {B} on {A}.", "Move {A}, {B}, and {C} to the center of the table using {a}, {b}, and {c}, then stack them.", "Using {a}, {b}, and {c}, bring {A}, {B}, and {C} to the center and stack {C} on {B}, {B} on {A}.", "Transfer {A}, {B}, and {C} to the center with {a}, {b}, and {c}, stacking {C} on {B} and {B} on {A}.", "Bring {A}, {B}, and {C} to the center point and arrange them by stacking {C} atop {B} and {B} atop {A}.", "Relocate {A}, {B}, and {C} to the table's center, stacking {C} over {B} and {B} over {A}.", "Move {A}, {B}, and {C} to the middle and position {C} on {B}, {B} on top of {A}.", "Place {A}, {B}, and {C} at the center, using {a}, {b}, and {c} to stack {C} on {B} and {B} on {A}.", "Transfer {A}, {B}, and {C} to the center, arranging {C} on top of {B} and {B} on {A} with {a}, {b}, {c}.", "Position {A}, {B}, and {C} centrally. Place {B} on {A}, then set {C} on {B}.", "Move {A}, {B}, and {<PERSON>} to the center. Stack {C} on {B} and {B} on {A}.", "Bring {A}, {B}, and {<PERSON>} to the middle. Stack {B} onto {A} and {C} onto {B}.", "Use {a}, {b}, and {c} to move {A}, {B}, and {C} to the center and stack them.", "Bring {A}, {B}, and {C} to the center using {a}, {b}, and {c}. Stack {B} on {A}.", "Use {a}, {b}, and {c} to place {A}, {B}, and {C} in the center. Stack {C} on top.", "With {a}, {b}, and {c}, move {A}, {B}, and {C} centrally and stack {B} on {A}.", "Use {a}, {b}, and {c} to centralize {A}, {B}, and {C} and build a stack with them.", "Place {A}, {B}, and {<PERSON>} in the center, then arrange {B} on {A} and {C} on {B}.", "Move {A}, {B}, and {C} to the table's center and stack {B} over {A}, {C} over {B}."], "unseen": ["Move {A}, {B}, and {<PERSON>} to the table's center and stack them.", "Transfer {A}, {B}, and {C} to the middle, then stack {C} over {B} and {B} over {A}.", "Move {A}, {B}, and {<PERSON>} to the center, then stack {C} on {B} and {B} on {A}.", "Bring {A}, {B}, {C} to the table's center and stack them: {C} on {B}, {B} on {A}.", "Place {A}, {B}, and {C} at the table's center, then stack {C} on {B} and {B} on {A}.", "Move {A}, {B}, and {<PERSON>} to the center, then stack {C} on {B}, and {B} on {A}.", "Move {A}, {B}, and {C} to the center of the table, then stack {C} on {B} and {B} on {A}.", "Bring {A}, {B}, and {<PERSON>} to the center, stacking {C} on {B} and {B} on {A}.", "Bring {A}, {B}, and {C} to the table's center. Stack {B} on {A} and {C} on {B}.", "Move {A}, {B}, and {<PERSON>} to the center, then stack {B} over {A} and {C} over {B}."]}