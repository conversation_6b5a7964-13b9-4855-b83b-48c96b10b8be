{"full_description": "Use arm to catch the qrcode board on the table, pick it up and rotate to let the qrcode face towards you", "schema": "{A} notifies the qrcode board. {a} notifies the arm to pick the qrcode board", "preference": "num of words should not exceed 15. Degree of detail avg is 6.", "seen": ["Pick up {A} and rotate it so the QR code faces you", "Use {a} to grab {A}, lift, and turn it QR code forward", "Lift {A} from the table and rotate it towards you", "Catch {A}, raise it, and turn it so the QR code faces you", "Grab {A}, lift it from the table, and rotate it QR front", "Use {a} to take {A} and turn it until the QR code faces you", "Lift {A} from the surface and adjust its angle towards you", "Employ {a} to seize {A}, raise it, and rotate it QR-forward", "Take {A}, lift it, and orient it so the QR faces you", "Use {a} to grab {A} and rotate it until the QR faces forward", "Find {A}, grab it, and turn it towards yourself.", "Use {a} to grab {A} and rotate the qrcode to face you.", "Slide {A} off the table and turn it to face you.", "Grab {A} with {a}, then rotate it to face yourself.", "Locate {A}, pick it up, and adjust its angle.", "Use {a} to lift {A} from the table and face the qrcode.", "Catch {A}, lift it, and turn the qrcode towards you.", "Grab {A} using {a}, then rotate it until the qrcode faces you.", "Pick up {A} and adjust its position to face the qrcode towards you.", "Lift {A} with {a}, then rotate it to make the qrcode visible.", "Catch and lift {A}, then turn it to show the QR code.", "Use {a} to grab {A} and rotate QR code towards you.", "Grab {A} using {a}, lift, and rotate until QR code faces you.", "Catch {A} with {a}, then rotate it to make the QR code visible.", "Lift {A} from the table and rotate it so the code faces you.", "Using {a}, catch {A} and rotate it to face the QR code.", "Catch {A} using {a}, pick it up, and turn it to face the QR code.", "Lift {A} and rotate it until the QR code faces you.", "Use {a} to grab {A}, rotate, and face the QR code towards you.", "Catch {A}, pick it up, and rotate to show the QR code.", "Catch {A}, lift it, and rotate it QR code facing.", "Use {a} to grab {A} and point its QR code toward you.", "Lift {A} from the table, turning it QR code forward.", "Take {A} from the table, rotating it QR code toward you.", "Use {a} to lift {A} and rotate it QR code toward you.", "Pick {A} up and turn its QR code toward you using {a}.", "Catch {A}, lift it, and adjust its QR code to face you.", "Grab {A} using {a}, then rotate the QR code to face forward.", "Lift {A} and orient its QR code toward you with {a}.", "Pick {A} up, rotate it, and ensure the QR code faces you.", "Lift {A} from the table and turn it to face you.", "Catch {A}, pick it up, and rotate to view the qrcode.", "Take {A}, raise it, and make the qrcode face you.", "Use {a} to pick {A} and turn it towards you.", "Lift {A} and rotate until its qrcode faces you.", "Catch {A} off the table and rotate its qrcode to you.", "Pick {A} up, then rotate to make its qrcode visible.", "Grab {A}, pick it up, and turn its qrcode toward you.", "Lift {A} and rotate for its qrcode to face you.", "Catch {A}, lift, and rotate to align the qrcode to you."], "unseen": ["Catch {A} from the table and rotate it", "Grab {A}, lift it, and turn it to face you", "Catch {A} from the table and make it face you.", "Pick {A} off the table using {a} and rotate it.", "Pick {A} up from the table and rotate it.", "Grab {A}, lift it, and rotate until the QR code faces you.", "Catch {A} on the table and pick it up.", "Pick up {A} and rotate it to face its QR code toward you.", "Pick up {A} and rotate it facing you.", "Grab {A}, lift it, and rotate to see the qrcode."]}