{"full_description": "use appropriate arm to place object A on the left of object B", "schema": "{A} notifies the object A, {B} notifies the object B, {a} notifies the arm to grab the object A", "preference": "num of words should not exceed 10.STRESS THE 'left'.", "seen": ["Grab {A} using {a} and place left of {B}.", "Place {A} on the left of {B}.", "With {a}, position {A} left of {B}.", "Position {A} to the left of {B}.", "Using {a}, move {A} left of {B}.", "Move {A} to the left of {B}.", "Use {a} and place {A} left of {B}.", "Set {A} left of {B}.", "With {a}, set {A} on the left of {B}.", "Carefully position {A} to the left of {B}.", "Place {A} left of {B} using {a}", "Move {A} to the left of {B}", "Put {A} on the left side of {B}", "Grab {A} with {a} and set left of {B}", "Shift {A} leftward to the side of {B}", "Use {a} to move {A} to {B}'s left", "Adjust {A} to rest on {B}'s left", "Use {a} to position {A} left of {B}", "Place {A} carefully to {B}'s left", "Grab {A} using {a} and move left of {B}", "Use {a}, move {A} left of {B}", "Position {A} to the left of {B}", "Place {A} exactly to the left of {B}", "Using {a}, put {A} on left of {B}", "Set {A} carefully on the left of {B}", "Move {A} to the left side of {B}", "Place {A} to rest left of {B} using {a}", "With {a}, position {A} left of {B}", "Set {A} directly to the left of {B}", "Using {a}, place {A} on the left of {B}", "Move {A} to the left of {B}.", "Grab {A} with {a} and shift it left of {B}.", "Place {A} directly to the left of {B}.", "With {a}, move {A} to the left side of {B}.", "Shift {A} and position it left of {B}.", "Use {a} to move {A} to the left of {B}.", "Set {A} on the left side of {B}.", "Grab {A} using {a} and place it left of {B}.", "Position {A} carefully to the left of {B}.", "Using {a}, set {A} to the left of {B}.", "Put {A} to the left of {B}", "Use {a} to set {A} left of {B}", "Drop {A} on the left side of {B}", "With {a}, stick {A} left of {B}", "Set {A} to the left of {B}", "Place {A} left of {B} using {a}", "Stick {A} on the left of {B}", "Use {a} to put {A} left of {B}", "Drop {A} to the left of {B}", "With {a}, place {A} left of {B}"], "unseen": ["Use {a} to put {A} left of {B}.", "Set {A} on the left side of {B}.", "Set {A} to the left of {B}", "Position {A} on the left of {B}", "Grab {A} and place left of {B}", "Set {A} down to the left of {B}", "Pick {A} and set it left of {B}.", "Use {a} to place {A} left of {B}.", "Set {A} on the left side of {B}", "Place {A} to the left of {B} using {a}"]}