{"full_description": "use one arm to pick up the can and another arm place it in the basket", "schema": "{A} notifies the can, {B} notifies the basket, {a} notifies the arm to pick up the can, the arm to pick up the basket use literal 'the other arm' or 'another arm'", "preference": "num of words should not exceed 15.Degree of detail avg 8.If {a} is mentioned in one description, 'the other arm' or 'another arm' must be mentioned in another description.Or otherwise both arm should not be mentioned. BUT {A} and {B} should always be mentioned in each description", "seen": ["Use {a} to lift {A}; another arm places it in {B}.", "Lift {A} and drop it into {B}.", "Grab {A} with an arm and move it to {B}.", "Hold {A} and stick it into {B}.", "Use {a} to pick up {A}, the other arm puts it in {B}.", "Lift {A} and carefully set it inside {B}.", "Grab {A} with {a} while another arm places it into {B}.", "Pick up {A} and drop it into {B}.", "Lift {A} using {a}, and use the other arm to set it in {B}.", "Pick up {A} before placing it inside {B}.", "Grab {A} and position it into {B} without switching arms.", "Lift {A} and carefully drop it into {B}.", "Pick up {A} with {a}, then place it into {B} with another arm.", "Lift {A} using one arm; use the other to set it in {B}.", "Take {A} and move it directly into {B}.", "Grab {A} and softly place it into {B}.", "Pick up {A} with {a}, then set it down into {B} with another arm.", "Lift {A} using {a}, then position it into {B} using the other arm.", "Take {A} from its spot and drop it into {B}.", "Grab {A} and set it into {B}.", "Move {A} to {B} after picking it up.", "Use {a} to grab {A} and put it in {B}.", "Place {A} into {B} after grabbing it.", "Grab {A} with one arm and drop it into {B}.", "Pick up {A} and place it inside {B}.", "Lift {A} with {a} and place it into {B}.", "Grab {A} and set it into {B}.", "Pick up {A} using one arm, place it in {B}.", "Lift {A} and position it into {B}.", "Use one arm to grab {A} and set it in {B}.", "Grab {A} using {a} and set it into {B}.", "Lift {A} and place it into {B}.", "Pick up {A} with {a} and move it to {B}.", "Move {A} into {B}.", "Use one arm to get {A} and another to place it in {B}.", "Place {A} into {B} after picking it up.", "Pick {A} using {a} and set it in {B}.", "Grab {A} and place it into {B}.", "Hold {A} with one arm, then put it in {B} using another.", "Set {A} into {B} after picking it up.", "Grab {A}, then place it directly into {B}.", "Lift {A} and carefully put it into {B}.", "Use {a} to hold {A} while the other arm places it in {B}.", "Pick up {A}, then transfer it to {B} using one arm.", "Raise {A} and set it inside {B} right away.", "Retrieve {A} and immediately place it into {B}.", "Hold {A} with {a} and use the other arm to put it in {B}.", "Grasp {A} using one arm, then drop it into {B}.", "Take {A} and move it safely into {B}.", "Pick up {A}, bring it to {B}, and place it inside."], "unseen": ["Grab {A} with {a} and place it in {B}.", "Pick up {A} and set it into {B}.", "Use {a} to grab {A}, then another arm to place it in {B}.", "Pick up {A} with one arm and set it into {B} using the other arm.", "Pick up {A} with {a} and set it in {B}.", "Grab {A} using one arm and drop it in {B}.", "Use {a} to grab {A} and place it in {B}.", "Pick up {A} and drop it into {B}.", "Use {a} to grab {A} and set it in {B}.", "Pick up {A} with one arm and drop it in {B}."]}