{"full_description": "if there is one bread on the table, use one arm to grab the bread and put it into the skillet", "schema": "{A} notifies the skillet, {B} notifies the bread, {a} notifies the arm to grab the bread", "preference": "num of words should not exceed 10. Degree of detail avg is 6", "seen": ["Grab {B} and place it inside {A}", "Pick up {B} with {a} and drop it in {A}", "Place {B} from the table into {A}", "Lift {B} using {a} and move it to {A}", "Move {B} into {A} from the table", "Pick {B} with {a} and set it in {A}", "Transfer {B} into {A} from the table", "Grab {B} using {a} and place it in {A}", "Take {B} and place it inside {A}", "Use {a} to pick {B} and put it in {A}", "Use {a} to grab {B} for {A}", "Place {B} into {A} using {a}", "Take {B} and set it into {A}", "Move {B} to {A} using {a}", "Pick up {B} and put it in {A}", "Grab {B} with {a} and place in {A}", "Use {a} to pick {B} up for {A}", "Set {B} into {A} after grabbing", "Put {B} into {A} after grabbing it", "Take {B} with {a} and drop into {A}", "Move {B} from table into {A}", "Use {a} to place {B} into {A}", "Lift {B} and drop it into {A}", "Pick {B} and transfer it to {A}", "Take {B} off the table into {A}", "Use {a} to move {B} into {A}", "Grab {B} and drop it into {A}", "Use {a} to grab {B} for {A}", "Take {B} and place it inside {A}", "Use {a} to pick {B} for {A}", "Pick up {B} and place it in {A}.", "Use {a} to set {B} into {A}.", "Take {B} and put it in {A}.", "Place {B} into {A} with {a}.", "Grab {B} from the table and set it in {A}.", "Pick {B} up using {a} and drop it in {A}.", "Put {B} into {A}.", "Grab {B} and place it inside {A}.", "Use {a} to move {B} and set it into {A}.", "Pick {B} off the table and place it in {A}.", "Take {B} and drop it into {A}.", "Use {a} to grab {B} and set it in {A}.", "Grab {B} and stick it in {A}.", "Use {a} to pick {B} and place it into {A}.", "Lift {B} and drop it into {A}.", "Use {a} to take {B} and move it into {A}.", "Pick {B} and set it into {A}.", "Use {a} to lift {B} and place it into {A}.", "Take {B} and put it in {A}.", "Use {a} to stick {B} into {A}."], "unseen": ["Take {B} and put it in {A}", "Use {a} to grab {B} and set it in {A}", "Grab {B} and place it in {A}", "Pick up {B} and drop it into {A}", "Pick {B} up and place it in {A}", "Grab {B} and set it in {A}", "Grab {B} and drop it into {A}.", "Move {B} to {A} using {a}.", "Grab {B} and place it into {A}.", "Use {a} to move {B} to {A}."]}