{"full_description": "use both arms to pick up the two shoes on the table and put them in the shoebox, with the shoe tip pointing to the left", "schema": "{A} notifies one shoe(use 'two {A}' or 'a pair of {}').{B} nofity the shoebox", "preference": "num of words should not exceed 10.IN EACH INSTRUCTION, YOU MUST STRESS THE SHOE TIP POINTING TO THE LEFT", "seen": ["Put two {A}, tips to the left, into {B}.", "Pick up two {A}, tips left, drop them into {B}.", "Collect two {A}, tips facing left, and put in {B}.", "Move two {A}, tip ends left, and set them in {B}.", "Transfer two {A}, tips left-aligned, into {B}.", "Use arms to lift two {A}, tips left, and set in {B}.", "Retrieve two {A}, orienting tips left, and place in {B}.", "Handle two {A}, tips directed left, and drop them into {B}.", "Pick two {A}, ensure tips facing left, and position in {B}.", "Grasp two {A}, tips aligned left, and place them in {B}.", "Grab two {A}, tip left, place them in {B}.", "Pick up two {A} and set them in {B}, tips left.", "Use hands to grab two {A}, tips left, drop into {B}.", "Move two {A} into {B}, ensure tips point left.", "Grab a pair of {A}, tips left, put them into {B}.", "Use both arms to place two {A} into {B}, tips left.", "Pick up two {A}, tips left, and set them in {B}.", "Place two {A} into {B}, ensure shoe tips face left.", "With both arms, move two {A} into {B}, tips facing left.", "Grab two {A}, tips left, place them carefully in {B}.", "Pick up two {A} together and place in {B}, tips left.", "Ensure two {A} are in {B}, tips facing left.", "Grab both {A} and put them into {B}, tips left.", "Set two {A} into the {B}, align tips to the left.", "Hold two {A}, drop them in {B}, tips pointing left.", "Place two {A} inside {B}, make tips face left.", "Lift two {A}, position them in {B}, tips to the left.", "Drop a pair of {A} into {B}, tips align left.", "Use both arms to set two {A} in {B}, tips left.", "Lay down two {A} in {B}, tips oriented left.", "Set two {A} in {B} with tips left.", "Hold two {A}, tips left, and place in {B}.", "Pick up two {A}, ensure tips point left.", "Put two {A} into {B}, shoe tips left.", "Make sure tips point left, then place {A} in {B}.", "Grab and place two {A} in {B}, tips left.", "Pick two {A} up from table, tips left.", "Stick both {A} in {B}, shoe tips pointing left.", "Place two {A} in {B}, tip left when set.", "Lift and put two {A} in {B}, tips left.", "Set two {A} in the {B}, tips pointing left.", "Place two {A} tips leftward into the {B}.", "Lift two {A}, ensure left tips, drop in {B}.", "Move a pair of {A} into {B}, left tips first.", "Slide both {A} into {B} with tips pointing left.", "Take two {A}, ensure tips left, place in {B}.", "Position two {A} into {B}, shoe tips leftward.", "Put two {A} in {B} with tips facing left.", "Arrange the two {A}, tips left, inside {B}.", "Grab both {A}, ensure tips left, place in {B}."], "unseen": ["Lift two {A}, tips pointing left, into {B}.", "Grab two {A}, point tips left, place in {B}.", "Pick up two {A}, tips left, into {B}.", "Use both arms to move two {A} to {B}, tips pointing left.", "Grab two {A} and set them in {B}, tips left.", "Put a pair of {A} in the {B}, tips pointing left.", "Grab two {A} with both hands, tip left.", "Place two {A} into {B}, tips left.", "Pick up two {A}, tips left, into {B}.", "Grab a pair of {A}, place left tips in {B}."]}